"use client";

import { useState, useEffect } from "react";
import { Monitor, Tablet, Smartphone, Download, ExternalLink, RefreshCw } from "lucide-react";
import { PlatformState } from "@/src/types";

interface PreviewCanvasProps {
  platformState: PlatformState;
}

export function PreviewCanvas({ platformState }: PreviewCanvasProps) {
  const [previewContent, setPreviewContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  // Get responsive dimensions based on preview mode
  const getCanvasDimensions = () => {
    switch (platformState.previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      default:
        return { width: '100%', height: '100%' };
    }
  };

  const dimensions = getCanvasDimensions();

  // Sample preview content (this will be replaced with actual generated content)
  useEffect(() => {
    if (platformState.currentProject) {
      // Load the actual generated website content
      setPreviewContent(`
        <!DOCTYPE html>
        <html lang="en" class="dark">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${platformState.currentProject.name}</title>
          <script src="https://cdn.tailwindcss.com"></script>
          <script>
            tailwind.config = {
              darkMode: 'class',
              theme: {
                extend: {}
              }
            }
          </script>
        </head>
        <body class="bg-gray-950 text-gray-50">
          <div class="min-h-screen flex items-center justify-center">
            <div class="text-center">
              <h1 class="text-4xl font-bold mb-4">Generated Website</h1>
              <p class="text-gray-400 mb-8">Your AI-generated website will appear here</p>
              <div class="space-y-4">
                <div class="w-64 h-4 bg-gray-800 rounded animate-pulse"></div>
                <div class="w-48 h-4 bg-gray-800 rounded animate-pulse"></div>
                <div class="w-56 h-4 bg-gray-800 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </body>
        </html>
      `);
    } else {
      // Default welcome content
      setPreviewContent(`
        <!DOCTYPE html>
        <html lang="en" class="dark">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>AI Web Dev Platform</title>
          <script src="https://cdn.tailwindcss.com"></script>
          <script>
            tailwind.config = {
              darkMode: 'class',
              theme: {
                extend: {}
              }
            }
          </script>
        </head>
        <body class="bg-gray-950 text-gray-50">
          <div class="min-h-screen flex items-center justify-center">
            <div class="text-center max-w-2xl mx-auto p-8">
              <div class="mb-8">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                  </svg>
                </div>
                <h1 class="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  AI Web Development Platform
                </h1>
                <p class="text-xl text-gray-400 mb-8">
                  Create production-ready websites through natural language conversation
                </p>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-gray-900 p-6 rounded-lg border border-gray-800">
                  <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold mb-2">Text to Website</h3>
                  <p class="text-gray-400 text-sm">Describe your vision and watch it come to life</p>
                </div>
                
                <div class="bg-gray-900 p-6 rounded-lg border border-gray-800">
                  <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold mb-2">URL Cloning</h3>
                  <p class="text-gray-400 text-sm">Recreate any website with pixel-perfect accuracy</p>
                </div>
                
                <div class="bg-gray-900 p-6 rounded-lg border border-gray-800">
                  <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold mb-2">Image to Code</h3>
                  <p class="text-gray-400 text-sm">Transform designs into functional websites</p>
                </div>
              </div>
              
              <p class="text-gray-500">
                Start by describing what you want to build in the chat sidebar →
              </p>
            </div>
          </div>
        </body>
        </html>
      `);
    }
  }, [platformState.currentProject]);

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const handleExport = () => {
    // This will be implemented in the export features task
    console.log('Export functionality will be implemented');
  };

  const handleOpenInNewTab = () => {
    const blob = new Blob([previewContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');
  };

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Preview Controls */}
      <div className="h-12 border-b border-gray-800 flex items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            {platformState.previewMode === 'desktop' && <Monitor className="w-4 h-4" />}
            {platformState.previewMode === 'tablet' && <Tablet className="w-4 h-4" />}
            {platformState.previewMode === 'mobile' && <Smartphone className="w-4 h-4" />}
            <span className="text-sm capitalize">{platformState.previewMode}</span>
          </div>
          {dimensions.width !== '100%' && (
            <span className="text-xs text-gray-500">
              {dimensions.width} × {dimensions.height}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 hover:bg-gray-800 rounded transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={handleOpenInNewTab}
            className="p-2 hover:bg-gray-800 rounded transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
          </button>
          <button
            onClick={handleExport}
            className="p-2 hover:bg-gray-800 rounded transition-colors"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Preview Frame */}
      <div className="flex-1 p-4 overflow-auto">
        <div 
          className="mx-auto bg-white rounded-lg shadow-2xl overflow-hidden"
          style={{
            width: dimensions.width,
            height: dimensions.height,
            maxWidth: '100%',
            maxHeight: '100%',
          }}
        >
          <iframe
            srcDoc={previewContent}
            className="w-full h-full border-0"
            title="Website Preview"
            sandbox="allow-scripts allow-same-origin"
          />
        </div>
      </div>
    </div>
  );
}
