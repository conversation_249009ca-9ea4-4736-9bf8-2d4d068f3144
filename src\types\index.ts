export interface WebsiteRequest {
  type: 'text' | 'url' | 'image';
  content: string;
  options?: {
    framework?: 'react' | 'nextjs' | 'vue' | 'svelte';
    styling?: 'tailwind' | 'css' | 'styled-components';
    theme?: 'light' | 'dark' | 'auto';
    responsive?: boolean;
    animations?: boolean;
  };
}

export interface GeneratedWebsite {
  id: string;
  name: string;
  description: string;
  files: GeneratedFile[];
  preview?: string;
  deployUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'component' | 'page' | 'style' | 'config' | 'asset';
  language: 'typescript' | 'javascript' | 'css' | 'html' | 'json';
}

export interface DesignPattern {
  name: string;
  description: string;
  cssClasses: string[];
  properties: Record<string, any>;
  examples: string[];
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  output?: string;
  error?: string;
  timestamp: Date;
}

export interface PlatformState {
  currentProject?: GeneratedWebsite;
  workflowSteps: WorkflowStep[];
  isGenerating: boolean;
  previewMode: 'desktop' | 'tablet' | 'mobile';
  sidebarCollapsed: boolean;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    type?: 'workflow' | 'error' | 'success';
    files?: string[];
    actions?: string[];
  };
}

export interface ExportOptions {
  format: 'zip' | 'github' | 'codesandbox';
  includeAssets: boolean;
  minify: boolean;
  deployment?: {
    platform: 'vercel' | 'netlify' | 'github-pages';
    config?: Record<string, any>;
  };
}
