"use client";

import { useState } from "react";
import { ChevronUp, ChevronDown, CheckCircle, Clock, AlertCircle, Loader2 } from "lucide-react";
import { WorkflowStep } from "@/src/types";
import { formatTimestamp } from "@/src/lib/utils";

interface WorkflowPanelProps {
  workflowSteps: WorkflowStep[];
  isGenerating: boolean;
}

export function WorkflowPanel({ workflowSteps, isGenerating }: WorkflowPanelProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [selectedStep, setSelectedStep] = useState<string | null>(null);

  const getStatusIcon = (status: WorkflowStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: WorkflowStep['status']) => {
    switch (status) {
      case 'completed':
        return 'border-green-400 bg-green-400/10';
      case 'running':
        return 'border-blue-400 bg-blue-400/10';
      case 'error':
        return 'border-red-400 bg-red-400/10';
      default:
        return 'border-gray-600 bg-gray-800/50';
    }
  };

  if (workflowSteps.length === 0 && !isGenerating) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-950 border-t border-gray-800 z-50">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-900/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isGenerating ? (
              <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 text-green-400" />
            )}
            <h3 className="font-semibold">
              {isGenerating ? 'AI Workflow in Progress' : 'Workflow Complete'}
            </h3>
          </div>
          
          {workflowSteps.length > 0 && (
            <div className="text-sm text-gray-400">
              {workflowSteps.filter(s => s.status === 'completed').length} / {workflowSteps.length} steps
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {isGenerating && (
            <div className="text-sm text-gray-400">
              Processing...
            </div>
          )}
          {isExpanded ? (
            <ChevronDown className="w-4 h-4" />
          ) : (
            <ChevronUp className="w-4 h-4" />
          )}
        </div>
      </div>

      {/* Workflow Steps */}
      {isExpanded && (
        <div className="max-h-64 overflow-y-auto border-t border-gray-800">
          <div className="p-4 space-y-3">
            {workflowSteps.map((step, index) => (
              <div key={step.id} className="flex items-start gap-3">
                {/* Step Number & Status */}
                <div className="flex flex-col items-center">
                  <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-semibold ${getStatusColor(step.status)}`}>
                    {step.status === 'running' || step.status === 'completed' ? (
                      getStatusIcon(step.status)
                    ) : (
                      index + 1
                    )}
                  </div>
                  {index < workflowSteps.length - 1 && (
                    <div className="w-0.5 h-6 bg-gray-700 mt-1" />
                  )}
                </div>

                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <div 
                    className="cursor-pointer"
                    onClick={() => setSelectedStep(selectedStep === step.id ? null : step.id)}
                  >
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">{step.name}</h4>
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(step.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mt-1">{step.description}</p>
                  </div>

                  {/* Expanded Step Details */}
                  {selectedStep === step.id && (
                    <div className="mt-3 p-3 bg-gray-900 rounded border border-gray-700">
                      {step.output && (
                        <div className="mb-3">
                          <h5 className="text-xs font-semibold text-gray-300 mb-1">Output:</h5>
                          <pre className="text-xs text-gray-400 whitespace-pre-wrap font-mono">
                            {step.output}
                          </pre>
                        </div>
                      )}
                      
                      {step.error && (
                        <div className="mb-3">
                          <h5 className="text-xs font-semibold text-red-300 mb-1">Error:</h5>
                          <pre className="text-xs text-red-400 whitespace-pre-wrap font-mono">
                            {step.error}
                          </pre>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Status: {step.status}</span>
                        <span>ID: {step.id}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* Current Step Indicator */}
            {isGenerating && (
              <div className="flex items-center gap-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded">
                <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />
                <div>
                  <div className="text-sm font-medium">AI is working...</div>
                  <div className="text-xs text-gray-400">
                    Analyzing requirements and generating code
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Progress Bar */}
      {workflowSteps.length > 0 && (
        <div className="h-1 bg-gray-800">
          <div 
            className="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-500"
            style={{
              width: `${(workflowSteps.filter(s => s.status === 'completed').length / workflowSteps.length) * 100}%`
            }}
          />
        </div>
      )}
    </div>
  );
}
