"use client";

import { useState } from "react";
import { Send, Paperclip, Image, Link, ChevronLeft, ChevronRight } from "lucide-react";
import { PlatformState, ChatMessage, WebsiteRequest } from "@/src/types";
import { generateId } from "@/src/lib/utils";

interface ChatSidebarProps {
  platformState: PlatformState;
  setPlatformState: (state: PlatformState | ((prev: PlatformState) => PlatformState)) => void;
}

export function ChatSidebar({ platformState, setPlatformState }: ChatSidebarProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: generateId(),
      role: 'assistant',
      content: 'Welcome to the AI Web Development Platform! I can help you create websites in three ways:\n\n1. **Text Description**: Describe what you want and I\'ll build it\n2. **URL Cloning**: Give me a website URL to recreate\n3. **Image to Code**: Upload a design image to convert\n\nWhat would you like to create today?',
      timestamp: new Date(),
    }
  ]);
  
  const [inputValue, setInputValue] = useState("");
  const [inputMode, setInputMode] = useState<'text' | 'url' | 'image'>('text');

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue("");

    // Set generating state
    setPlatformState(prev => ({ ...prev, isGenerating: true }));

    // Create website request
    const request: WebsiteRequest = {
      type: inputMode,
      content: inputValue,
      options: {
        framework: 'nextjs',
        styling: 'tailwind',
        theme: 'dark',
        responsive: true,
        animations: true,
      }
    };

    // Simulate AI processing (this will be replaced with actual Mastra agent calls)
    setTimeout(() => {
      const assistantMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: `I'll help you create a website based on your ${inputMode} input. Let me break this down into steps:\n\n1. Analyzing your requirements\n2. Generating component structure\n3. Creating responsive layouts\n4. Adding styling and animations\n5. Setting up the preview\n\nStarting the generation process...`,
        timestamp: new Date(),
        metadata: {
          type: 'workflow',
          actions: ['analyze', 'generate', 'style', 'preview']
        }
      };

      setMessages(prev => [...prev, assistantMessage]);
      
      // This will be replaced with actual workflow steps
      setPlatformState(prev => ({
        ...prev,
        workflowSteps: [
          {
            id: generateId(),
            name: 'Analyzing Requirements',
            description: 'Understanding your request and planning the website structure',
            status: 'running',
            timestamp: new Date(),
          }
        ]
      }));
    }, 1000);
  };

  const toggleSidebar = () => {
    setPlatformState(prev => ({
      ...prev,
      sidebarCollapsed: !prev.sidebarCollapsed
    }));
  };

  if (platformState.sidebarCollapsed) {
    return (
      <div className="w-12 h-full flex flex-col items-center py-4">
        <button
          onClick={toggleSidebar}
          className="p-2 hover:bg-gray-800 rounded transition-colors"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    );
  }

  return (
    <div className="w-96 h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-800 flex items-center justify-between">
        <h2 className="font-semibold">AI Assistant</h2>
        <button
          onClick={toggleSidebar}
          className="p-1 hover:bg-gray-800 rounded transition-colors"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-100'
              }`}
            >
              <div className="text-sm whitespace-pre-wrap">{message.content}</div>
              <div className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Input Mode Selector */}
      <div className="p-4 border-t border-gray-800">
        <div className="flex gap-2 mb-3">
          <button
            onClick={() => setInputMode('text')}
            className={`flex-1 py-2 px-3 rounded text-sm transition-colors ${
              inputMode === 'text' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-800 hover:bg-gray-700'
            }`}
          >
            Text
          </button>
          <button
            onClick={() => setInputMode('url')}
            className={`flex-1 py-2 px-3 rounded text-sm transition-colors ${
              inputMode === 'url' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-800 hover:bg-gray-700'
            }`}
          >
            URL
          </button>
          <button
            onClick={() => setInputMode('image')}
            className={`flex-1 py-2 px-3 rounded text-sm transition-colors ${
              inputMode === 'image' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-800 hover:bg-gray-700'
            }`}
          >
            Image
          </button>
        </div>

        {/* Input Area */}
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={
                inputMode === 'text' 
                  ? "Describe the website you want to create..."
                  : inputMode === 'url'
                  ? "Enter a website URL to clone..."
                  : "Upload an image or describe it..."
              }
              className="w-full p-3 bg-gray-800 border border-gray-700 rounded resize-none focus:outline-none focus:border-blue-500"
              rows={3}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || platformState.isGenerating}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-700 disabled:cursor-not-allowed rounded transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
