import { VibeKit } from "@vibe-kit/sdk";
import { createE2B<PERSON>rovider } from "@vibe-kit/e2b";

let vibeKitInstance: VibeKit | null = null;

export function createVibeKitInstance() {
  if (vibeKitInstance) {
    return vibeKitInstance;
  }

  const e2bProvider = createE2BProvider({
    apiKey: process.env.E2B_API_KEY!,
    templateId: "vibekit-claude",
  });

  vibeKitInstance = new VibeKit()
    .withAgent({
      type: "claude",
      provider: "anthropic",
      apiKey: process.env.ANTHROPIC_API_KEY!,
      model: "claude-sonnet-4-20250514",
    })
    .withSandbox(e2bProvider);

  // Add event listeners for real-time updates
  vibeKitInstance.on("update", (update) => {
    console.log("VibeKit Update:", update);
    // This will be connected to our real-time UI updates
  });

  vibeKitInstance.on("error", (error) => {
    console.error("VibeKit Error:", error);
    // This will be connected to our error handling system
  });

  return vibeKitInstance;
}

export async function generateCodeWithVibeKit(prompt: string, mode: 'code' | 'ask' = 'code') {
  const vibeKit = createVibeKitInstance();
  
  try {
    const result = await vibeKit.generateCode({
      prompt,
      mode,
    });

    return result;
  } catch (error) {
    console.error('Error generating code with VibeKit:', error);
    throw error;
  }
}

export async function getVibeKitHost(port: number = 3000) {
  const vibeKit = createVibeKitInstance();
  return await vibeKit.getHost(port);
}

export async function cleanupVibeKit() {
  if (vibeKitInstance) {
    await vibeKitInstance.kill();
    vibeKitInstance = null;
  }
}
