import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const codeGeneratorTool = createTool({
  id: "generate-code",
  description: "Generate React/Next.js components and pages with Tailwind CSS styling based on design specifications",
  inputSchema: z.object({
    componentType: z.enum(["page", "component", "layout", "style"]).describe("Type of code to generate"),
    specifications: z.object({
      name: z.string().describe("Component or page name"),
      description: z.string().describe("Detailed description of functionality"),
      styling: z.object({
        colors: z.array(z.string()).optional(),
        typography: z.record(z.string()).optional(),
        spacing: z.string().optional(),
        animations: z.array(z.string()).optional()
      }).optional(),
      layout: z.object({
        structure: z.string(),
        sections: z.array(z.string()),
        responsive: z.boolean().default(true)
      }).optional(),
      functionality: z.array(z.string()).optional().describe("Interactive features needed")
    }),
    framework: z.enum(["react", "nextjs"]).default("nextjs"),
    typescript: z.boolean().default(true),
    includeTests: z.boolean().default(false)
  }),
  outputSchema: z.object({
    files: z.array(z.object({
      path: z.string().describe("File path relative to project root"),
      content: z.string().describe("Generated code content"),
      type: z.enum(["component", "page", "style", "test", "config"]),
      language: z.enum(["typescript", "javascript", "css", "json"])
    })),
    dependencies: z.array(z.string()).describe("Required npm packages"),
    instructions: z.array(z.string()).describe("Setup and usage instructions"),
    preview: z.string().optional().describe("Preview description or URL")
  }),
  execute: async ({ componentType, specifications, framework, typescript, includeTests }) => {
    // This is a placeholder implementation
    // In the actual implementation, this would:
    // 1. Use VibeKit for secure code generation
    // 2. Generate React/Next.js components with TypeScript
    // 3. Apply Tailwind CSS styling
    // 4. Ensure responsive design
    // 5. Include proper accessibility attributes
    // 6. Generate tests if requested
    
    console.log(`Generating ${componentType} with framework: ${framework}`);
    
    const fileExtension = typescript ? 'tsx' : 'jsx';
    const componentName = specifications.name;
    
    // Simulated code generation
    const generatedFiles = [
      {
        path: `components/${componentName}.${fileExtension}`,
        content: `import React from 'react';

interface ${componentName}Props {
  // Props will be defined based on specifications
}

export function ${componentName}(props: ${componentName}Props) {
  return (
    <div className="p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
        ${specifications.name}
      </h2>
      <p className="text-gray-600 dark:text-gray-300">
        ${specifications.description}
      </p>
      {/* Generated content based on specifications */}
    </div>
  );
}

export default ${componentName};`,
        type: "component" as const,
        language: "typescript" as const
      }
    ];

    if (includeTests) {
      generatedFiles.push({
        path: `__tests__/${componentName}.test.${fileExtension}`,
        content: `import { render, screen } from '@testing-library/react';
import { ${componentName} } from '../components/${componentName}';

describe('${componentName}', () => {
  it('renders correctly', () => {
    render(<${componentName} />);
    expect(screen.getByText('${specifications.name}')).toBeInTheDocument();
  });
});`,
        type: "test" as const,
        language: "typescript" as const
      });
    }

    return {
      files: generatedFiles,
      dependencies: [
        "react",
        "react-dom",
        ...(framework === "nextjs" ? ["next"] : []),
        ...(typescript ? ["typescript", "@types/react", "@types/react-dom"] : []),
        "tailwindcss",
        ...(includeTests ? ["@testing-library/react", "@testing-library/jest-dom"] : [])
      ],
      instructions: [
        `Install dependencies: npm install ${generatedFiles.length > 1 ? 'dependencies' : 'dependency'}`,
        `Import and use the ${componentName} component in your application`,
        ...(specifications.styling ? ["Apply custom styling as needed"] : []),
        ...(includeTests ? ["Run tests with: npm test"] : [])
      ],
      preview: `Generated ${componentType} with responsive design and dark mode support`
    };
  }
});
