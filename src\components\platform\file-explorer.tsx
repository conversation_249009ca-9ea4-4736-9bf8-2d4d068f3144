"use client";

import { useState } from "react";
import { ChevronRight, ChevronDown, File, Folder, FolderOpen } from "lucide-react";
import { GeneratedWebsite, GeneratedFile } from "@/src/types";

interface FileExplorerProps {
  currentProject?: GeneratedWebsite;
}

interface FileTreeNode {
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileTreeNode[];
  file?: GeneratedFile;
}

export function FileExplorer({ currentProject }: FileExplorerProps) {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['/', '/components', '/pages']));
  const [selectedFile, setSelectedFile] = useState<string | null>(null);

  // Build file tree from project files
  const buildFileTree = (files: GeneratedFile[]): FileTreeNode[] => {
    const tree: FileTreeNode[] = [];
    const folderMap = new Map<string, FileTreeNode>();

    // Create root folders
    const rootFolders = ['components', 'pages', 'styles', 'public', 'lib'];
    rootFolders.forEach(folder => {
      const node: FileTreeNode = {
        name: folder,
        type: 'folder',
        path: `/${folder}`,
        children: []
      };
      tree.push(node);
      folderMap.set(`/${folder}`, node);
    });

    // Add files to appropriate folders
    files.forEach(file => {
      const pathParts = file.path.split('/');
      const fileName = pathParts[pathParts.length - 1];
      const folderPath = '/' + pathParts.slice(0, -1).join('/').replace(/^\//, '');
      
      let parentFolder = folderMap.get(folderPath);
      if (!parentFolder) {
        // Create missing folders
        const folderName = pathParts[pathParts.length - 2] || 'root';
        parentFolder = {
          name: folderName,
          type: 'folder',
          path: folderPath,
          children: []
        };
        tree.push(parentFolder);
        folderMap.set(folderPath, parentFolder);
      }

      const fileNode: FileTreeNode = {
        name: fileName,
        type: 'file',
        path: file.path,
        file
      };

      parentFolder.children?.push(fileNode);
    });

    return tree;
  };

  const toggleFolder = (path: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const getFileIcon = (file: GeneratedFile) => {
    const extension = file.path.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'tsx':
      case 'jsx':
        return '⚛️';
      case 'ts':
      case 'js':
        return '📜';
      case 'css':
        return '🎨';
      case 'json':
        return '📋';
      case 'md':
        return '📝';
      default:
        return '📄';
    }
  };

  const renderFileTree = (nodes: FileTreeNode[], depth = 0) => {
    return nodes.map((node) => (
      <div key={node.path}>
        <div
          className={`flex items-center gap-2 py-1 px-2 hover:bg-gray-800 cursor-pointer ${
            selectedFile === node.path ? 'bg-blue-600/20 border-r-2 border-blue-500' : ''
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path);
            } else {
              setSelectedFile(node.path);
            }
          }}
        >
          {node.type === 'folder' ? (
            <>
              {expandedFolders.has(node.path) ? (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-400" />
              )}
              {expandedFolders.has(node.path) ? (
                <FolderOpen className="w-4 h-4 text-blue-400" />
              ) : (
                <Folder className="w-4 h-4 text-blue-400" />
              )}
            </>
          ) : (
            <>
              <div className="w-4" />
              <span className="text-sm">{node.file ? getFileIcon(node.file) : '📄'}</span>
            </>
          )}
          <span className="text-sm truncate">{node.name}</span>
        </div>
        
        {node.type === 'folder' && expandedFolders.has(node.path) && node.children && (
          <div>
            {renderFileTree(node.children, depth + 1)}
          </div>
        )}
      </div>
    ));
  };

  // Sample file structure when no project is loaded
  const sampleFiles: GeneratedFile[] = [
    {
      path: '/pages/index.tsx',
      content: 'export default function Home() { return <div>Home</div>; }',
      type: 'page',
      language: 'typescript'
    },
    {
      path: '/components/Header.tsx',
      content: 'export function Header() { return <header>Header</header>; }',
      type: 'component',
      language: 'typescript'
    },
    {
      path: '/styles/globals.css',
      content: 'body { margin: 0; }',
      type: 'style',
      language: 'css'
    }
  ];

  const files = currentProject?.files || sampleFiles;
  const fileTree = buildFileTree(files);

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Header */}
      <div className="p-3 border-b border-gray-800">
        <h3 className="text-sm font-semibold text-gray-300">
          {currentProject ? currentProject.name : 'Project Files'}
        </h3>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {fileTree.length > 0 ? (
          <div className="py-2">
            {renderFileTree(fileTree)}
          </div>
        ) : (
          <div className="p-4 text-center text-gray-500">
            <File className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No files generated yet</p>
            <p className="text-xs mt-1">Start a conversation to create your first website</p>
          </div>
        )}
      </div>

      {/* File Actions */}
      {selectedFile && (
        <div className="p-3 border-t border-gray-800">
          <div className="text-xs text-gray-400 mb-2">Selected: {selectedFile}</div>
          <div className="flex gap-2">
            <button className="px-2 py-1 text-xs bg-gray-800 hover:bg-gray-700 rounded transition-colors">
              View
            </button>
            <button className="px-2 py-1 text-xs bg-gray-800 hover:bg-gray-700 rounded transition-colors">
              Edit
            </button>
            <button className="px-2 py-1 text-xs bg-gray-800 hover:bg-gray-700 rounded transition-colors">
              Download
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
