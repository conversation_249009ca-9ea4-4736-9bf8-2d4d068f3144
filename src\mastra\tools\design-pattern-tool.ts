import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const designPatternTool = createTool({
  id: "interpret-design-pattern",
  description: "Interpret complex design patterns and aesthetic instructions into implementable CSS/Tailwind classes",
  inputSchema: z.object({
    pattern: z.string().describe("Design pattern name or description (e.g., 'glassmorphism', 'neumorphism', 'brutalism')"),
    context: z.string().describe("Context where the pattern will be applied (e.g., 'navigation bar', 'card component', 'hero section')"),
    colorScheme: z.enum(["light", "dark", "auto"]).default("dark").describe("Color scheme preference"),
    intensity: z.enum(["subtle", "moderate", "bold"]).default("moderate").describe("Pattern intensity level"),
    customProperties: z.record(z.string()).optional().describe("Custom properties to override defaults")
  }),
  outputSchema: z.object({
    pattern: z.object({
      name: z.string().describe("Standardized pattern name"),
      description: z.string().describe("Pattern description and use cases"),
      category: z.string().describe("Design category (modern, retro, minimal, etc.)")
    }),
    implementation: z.object({
      tailwindClasses: z.array(z.string()).describe("Tailwind CSS classes to apply"),
      customCSS: z.string().optional().describe("Additional custom CSS if needed"),
      htmlStructure: z.string().optional().describe("Recommended HTML structure"),
      animations: z.array(z.string()).optional().describe("Animation classes or keyframes")
    }),
    variations: z.array(z.object({
      name: z.string(),
      description: z.string(),
      classes: z.array(z.string())
    })).describe("Pattern variations for different use cases"),
    compatibility: z.object({
      responsive: z.boolean().describe("Whether pattern works well responsively"),
      accessibility: z.string().describe("Accessibility considerations"),
      performance: z.string().describe("Performance impact notes")
    })
  }),
  execute: async ({ pattern, context, colorScheme, intensity, customProperties }) => {
    // This is a placeholder implementation
    // In the actual implementation, this would:
    // 1. Parse and understand design pattern terminology
    // 2. Map patterns to appropriate CSS/Tailwind implementations
    // 3. Consider context and apply appropriate variations
    // 4. Generate responsive and accessible implementations
    // 5. Provide performance-optimized solutions
    
    console.log(`Interpreting design pattern: ${pattern} for ${context}`);
    
    // Pattern interpretation logic
    const patternMap: Record<string, any> = {
      glassmorphism: {
        name: "Glassmorphism",
        description: "Translucent glass-like effect with backdrop blur and subtle borders",
        category: "modern",
        baseClasses: ["backdrop-blur-md", "bg-white/10", "border", "border-white/20", "rounded-xl"],
        darkClasses: ["bg-white/5", "border-white/10"],
        lightClasses: ["bg-black/5", "border-black/10"]
      },
      neumorphism: {
        name: "Neumorphism",
        description: "Soft, extruded shapes that appear to emerge from the background",
        category: "modern",
        baseClasses: ["rounded-2xl", "shadow-inner"],
        darkClasses: ["bg-gray-800", "shadow-gray-900/50"],
        lightClasses: ["bg-gray-100", "shadow-gray-300/50"]
      },
      brutalism: {
        name: "Brutalism",
        description: "Bold, raw, and unapologetic design with sharp edges and high contrast",
        category: "bold",
        baseClasses: ["border-4", "border-black", "bg-yellow-400", "text-black", "font-black"],
        darkClasses: ["border-white", "bg-red-500", "text-white"],
        lightClasses: ["border-black", "bg-yellow-400", "text-black"]
      }
    };

    const normalizedPattern = pattern.toLowerCase();
    const matchedPattern = patternMap[normalizedPattern] || {
      name: "Custom Pattern",
      description: `Custom design pattern: ${pattern}`,
      category: "custom",
      baseClasses: ["rounded-lg", "shadow-md"],
      darkClasses: ["bg-gray-800", "text-white"],
      lightClasses: ["bg-white", "text-gray-900"]
    };

    // Apply intensity modifications
    const intensityModifiers = {
      subtle: { opacity: "opacity-70", blur: "backdrop-blur-sm", shadow: "shadow-sm" },
      moderate: { opacity: "opacity-90", blur: "backdrop-blur-md", shadow: "shadow-md" },
      bold: { opacity: "opacity-100", blur: "backdrop-blur-lg", shadow: "shadow-xl" }
    };

    const modifier = intensityModifiers[intensity];
    
    // Build final classes based on color scheme
    let finalClasses = [...matchedPattern.baseClasses];
    
    if (colorScheme === "dark") {
      finalClasses.push(...matchedPattern.darkClasses);
    } else if (colorScheme === "light") {
      finalClasses.push(...matchedPattern.lightClasses);
    } else {
      // Auto mode - include both with dark: prefix
      finalClasses.push(...matchedPattern.lightClasses);
      finalClasses.push(...matchedPattern.darkClasses.map((cls: string) => `dark:${cls}`));
    }

    // Apply intensity modifiers
    if (normalizedPattern === "glassmorphism") {
      finalClasses.push(modifier.blur, modifier.opacity);
    }
    finalClasses.push(modifier.shadow);

    // Context-specific adjustments
    if (context.includes("navigation") || context.includes("nav")) {
      finalClasses.push("sticky", "top-0", "z-50");
    }
    if (context.includes("card")) {
      finalClasses.push("p-6", "transition-transform", "hover:scale-105");
    }
    if (context.includes("hero")) {
      finalClasses.push("min-h-screen", "flex", "items-center", "justify-center");
    }

    return {
      pattern: {
        name: matchedPattern.name,
        description: matchedPattern.description,
        category: matchedPattern.category
      },
      implementation: {
        tailwindClasses: finalClasses,
        customCSS: normalizedPattern === "neumorphism" ? `
          .neumorphic {
            box-shadow: 
              20px 20px 60px #bebebe,
              -20px -20px 60px #ffffff;
          }
          .dark .neumorphic {
            box-shadow: 
              20px 20px 60px #1a1a1a,
              -20px -20px 60px #2a2a2a;
          }
        ` : undefined,
        htmlStructure: context.includes("card") ? 
          `<div class="${finalClasses.join(' ')}">
            <h3>Card Title</h3>
            <p>Card content</p>
          </div>` : undefined,
        animations: normalizedPattern === "glassmorphism" ? 
          ["transition-all", "duration-300", "hover:backdrop-blur-lg"] : 
          ["transition-all", "duration-200"]
      },
      variations: [
        {
          name: "Subtle",
          description: "Reduced opacity and effects",
          classes: finalClasses.map(cls => 
            cls.includes("opacity") ? "opacity-50" : 
            cls.includes("blur") ? "backdrop-blur-sm" : cls
          )
        },
        {
          name: "Interactive",
          description: "Enhanced hover and focus states",
          classes: [...finalClasses, "hover:scale-105", "focus:ring-2", "focus:ring-blue-500"]
        }
      ],
      compatibility: {
        responsive: true,
        accessibility: normalizedPattern === "glassmorphism" ? 
          "Ensure sufficient contrast ratios with backdrop content" :
          "Pattern maintains good contrast and readability",
        performance: normalizedPattern === "glassmorphism" ?
          "Backdrop blur may impact performance on older devices" :
          "Minimal performance impact"
      }
    };
  }
});
