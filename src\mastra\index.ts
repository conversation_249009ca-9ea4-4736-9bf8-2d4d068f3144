import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { webDevAgent } from "./agents/web-dev-agent";
import { urlAnalyzerTool } from "./tools/url-analyzer-tool";
import { imageAnalyzerTool } from "./tools/image-analyzer-tool";
import { codeGeneratorTool } from "./tools/code-generator-tool";
import { designPatternTool } from "./tools/design-pattern-tool";

export const mastra = new Mastra({
  agents: { 
    webDevAgent 
  },
  tools: {
    urlAnalyzerTool,
    imageAnalyzerTool,
    codeGeneratorTool,
    designPatternTool
  }
});
