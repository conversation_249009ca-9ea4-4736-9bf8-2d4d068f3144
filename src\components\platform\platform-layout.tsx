"use client";

import { useState } from "react";
import { ChatSidebar } from "./chat-sidebar";
import { PreviewCanvas } from "./preview-canvas";
import { FileExplorer } from "./file-explorer";
import { WorkflowPanel } from "./workflow-panel";
import { PlatformState } from "@/src/types";

export function PlatformLayout() {
  const [platformState, setPlatformState] = useState<PlatformState>({
    workflowSteps: [],
    isGenerating: false,
    previewMode: 'desktop',
    sidebarCollapsed: false,
  });

  const [showFileExplorer, setShowFileExplorer] = useState(false);

  return (
    <div className="h-screen flex bg-gray-950 text-gray-50">
      {/* Left Sidebar - Chat Interface */}
      <div className={`transition-all duration-300 ${
        platformState.sidebarCollapsed ? 'w-12' : 'w-96'
      } border-r border-gray-800 flex flex-col`}>
        <ChatSidebar 
          platformState={platformState}
          setPlatformState={setPlatformState}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="h-14 border-b border-gray-800 flex items-center justify-between px-4">
          <div className="flex items-center gap-4">
            <h1 className="text-lg font-semibold">AI Web Dev Platform</h1>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowFileExplorer(!showFileExplorer)}
                className="px-3 py-1 text-sm bg-gray-800 hover:bg-gray-700 rounded transition-colors"
              >
                {showFileExplorer ? 'Hide Files' : 'Show Files'}
              </button>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <select 
              value={platformState.previewMode}
              onChange={(e) => setPlatformState(prev => ({
                ...prev,
                previewMode: e.target.value as 'desktop' | 'tablet' | 'mobile'
              }))}
              className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm"
            >
              <option value="desktop">Desktop</option>
              <option value="tablet">Tablet</option>
              <option value="mobile">Mobile</option>
            </select>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex">
          {/* File Explorer (Optional) */}
          {showFileExplorer && (
            <div className="w-64 border-r border-gray-800">
              <FileExplorer 
                currentProject={platformState.currentProject}
              />
            </div>
          )}

          {/* Preview Canvas */}
          <div className="flex-1">
            <PreviewCanvas 
              platformState={platformState}
            />
          </div>
        </div>
      </div>

      {/* Workflow Panel (Bottom) */}
      <WorkflowPanel 
        workflowSteps={platformState.workflowSteps}
        isGenerating={platformState.isGenerating}
      />
    </div>
  );
}
